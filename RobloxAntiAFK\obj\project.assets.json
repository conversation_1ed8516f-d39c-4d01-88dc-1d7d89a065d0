{"version": 3, "targets": {"net8.0-windows7.0": {"CommunityToolkit.Mvvm/8.4.0": {"type": "package", "compile": {"lib/net8.0/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/CommunityToolkit.Mvvm.targets": {}}}, "Hardcodet.NotifyIcon.Wpf/2.0.1": {"type": "package", "compile": {"lib/net8.0-windows7.0/Hardcodet.NotifyIcon.Wpf.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Hardcodet.NotifyIcon.Wpf.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App"]}, "Microsoft.Xaml.Behaviors.Wpf/1.1.135": {"type": "package", "compile": {"lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "System.CodeDom/9.0.6": {"type": "package", "compile": {"lib/net8.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Management/9.0.6": {"type": "package", "dependencies": {"System.CodeDom": "9.0.6"}, "compile": {"lib/net8.0/System.Management.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Management.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Management.dll": {"assetType": "runtime", "rid": "win"}}}}}, "libraries": {"CommunityToolkit.Mvvm/8.4.0": {"sha512": "tqVU8yc/ADO9oiTRyTnwhFN68hCwvkliMierptWOudIAvWY1mWCh5VFh+guwHJmpMwfg0J0rY+yyd5Oy7ty9Uw==", "type": "package", "path": "communitytoolkit.mvvm/8.4.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "License.md", "ThirdPartyNotices.txt", "analyzers/dotnet/roslyn4.0/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.0/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "analyzers/dotnet/roslyn4.12/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.12/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "analyzers/dotnet/roslyn4.3/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.3/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "build/CommunityToolkit.Mvvm.FeatureSwitches.targets", "build/CommunityToolkit.Mvvm.SourceGenerators.targets", "build/CommunityToolkit.Mvvm.Windows.targets", "build/CommunityToolkit.Mvvm.WindowsSdk.targets", "build/CommunityToolkit.Mvvm.targets", "buildTransitive/CommunityToolkit.Mvvm.FeatureSwitches.targets", "buildTransitive/CommunityToolkit.Mvvm.SourceGenerators.targets", "buildTransitive/CommunityToolkit.Mvvm.Windows.targets", "buildTransitive/CommunityToolkit.Mvvm.WindowsSdk.targets", "buildTransitive/CommunityToolkit.Mvvm.targets", "communitytoolkit.mvvm.8.4.0.nupkg.sha512", "communitytoolkit.mvvm.nuspec", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.dll", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.pdb", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.xml", "lib/net8.0/CommunityToolkit.Mvvm.dll", "lib/net8.0/CommunityToolkit.Mvvm.pdb", "lib/net8.0/CommunityToolkit.Mvvm.xml", "lib/netstandard2.0/CommunityToolkit.Mvvm.dll", "lib/netstandard2.0/CommunityToolkit.Mvvm.pdb", "lib/netstandard2.0/CommunityToolkit.Mvvm.xml", "lib/netstandard2.1/CommunityToolkit.Mvvm.dll", "lib/netstandard2.1/CommunityToolkit.Mvvm.pdb", "lib/netstandard2.1/CommunityToolkit.Mvvm.xml"]}, "Hardcodet.NotifyIcon.Wpf/2.0.1": {"sha512": "dtxmeZXzV2GzSm91aZ3hqzgoeVoARSkDPVCYfhVUNyyKBWYxMgNC0EcLiSYxD4Uc4alq/2qb3SmV8DgAENLRLQ==", "type": "package", "path": "hardcodet.notifyicon.wpf/2.0.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "hardcodet.notifyicon.wpf.2.0.1.nupkg.sha512", "hardcodet.notifyicon.wpf.nuspec", "icon.png", "lib/net462/Hardcodet.NotifyIcon.Wpf.dll", "lib/net462/Hardcodet.NotifyIcon.Wpf.xml", "lib/net472/Hardcodet.NotifyIcon.Wpf.dll", "lib/net472/Hardcodet.NotifyIcon.Wpf.xml", "lib/net6.0-windows7.0/Hardcodet.NotifyIcon.Wpf.dll", "lib/net6.0-windows7.0/Hardcodet.NotifyIcon.Wpf.xml", "lib/net8.0-windows7.0/Hardcodet.NotifyIcon.Wpf.dll", "lib/net8.0-windows7.0/Hardcodet.NotifyIcon.Wpf.xml"]}, "Microsoft.Xaml.Behaviors.Wpf/1.1.135": {"sha512": "r8qBEXmQfORso2+MVHnt8PSH4761zJ0SIxgQTSEDVLU97EN2FZdG6/ZCYUPhQy+OrPKgnpYBCAs3PS6Bs7wRsg==", "type": "package", "path": "microsoft.xaml.behaviors.wpf/1.1.135", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/Design/Microsoft.Xaml.Behaviors.Design.dll", "lib/net462/Design/Microsoft.Xaml.Behaviors.DesignTools.dll", "lib/net462/Microsoft.Xaml.Behaviors.dll", "lib/net462/Microsoft.Xaml.Behaviors.pdb", "lib/net462/Microsoft.Xaml.Behaviors.xml", "lib/net6.0-windows7.0/Design/Microsoft.Xaml.Behaviors.DesignTools.dll", "lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.dll", "lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.pdb", "lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.xml", "microsoft.xaml.behaviors.wpf.1.1.135.nupkg.sha512", "microsoft.xaml.behaviors.wpf.nuspec", "tools/Install.ps1"]}, "System.CodeDom/9.0.6": {"sha512": "9u1pyEykc0bWBHf1cIVwRoMqrEtxtXdC2ss1K02pvrkwHPcyYmy1glO+kLbyqPO9ehCTl+2dFyUuTUNl1Fde5g==", "type": "package", "path": "system.codedom/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.CodeDom.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.CodeDom.targets", "lib/net462/System.CodeDom.dll", "lib/net462/System.CodeDom.xml", "lib/net8.0/System.CodeDom.dll", "lib/net8.0/System.CodeDom.xml", "lib/net9.0/System.CodeDom.dll", "lib/net9.0/System.CodeDom.xml", "lib/netstandard2.0/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.xml", "system.codedom.9.0.6.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt"]}, "System.Management/9.0.6": {"sha512": "pOc5bCurWL3qVDsPP0iycUCRfXBhI/fVe44SiAlVqoZDIbHP080CLyCzfXV1UbdGKN0hQSLSHqr7OI3BhLBRbA==", "type": "package", "path": "system.management/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Management.targets", "lib/net462/_._", "lib/net8.0/System.Management.dll", "lib/net8.0/System.Management.xml", "lib/net9.0/System.Management.dll", "lib/net9.0/System.Management.xml", "lib/netstandard2.0/System.Management.dll", "lib/netstandard2.0/System.Management.xml", "runtimes/win/lib/net8.0/System.Management.dll", "runtimes/win/lib/net8.0/System.Management.xml", "runtimes/win/lib/net9.0/System.Management.dll", "runtimes/win/lib/net9.0/System.Management.xml", "system.management.9.0.6.nupkg.sha512", "system.management.nuspec", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net8.0-windows7.0": ["CommunityToolkit.Mvvm >= 8.4.0", "Hardcodet.NotifyIcon.Wpf >= 2.0.1", "Microsoft.Xaml.Behaviors.Wpf >= 1.1.135", "System.Management >= 9.0.6"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "Z:\\rblxantiafk\\RobloxAntiAFK\\RobloxAntiAFK.csproj", "projectName": "RobloxAntiAFK", "projectPath": "Z:\\rblxantiafk\\RobloxAntiAFK\\RobloxAntiAFK.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "Z:\\rblxantiafk\\RobloxAntiAFK\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.4.0, )"}, "Hardcodet.NotifyIcon.Wpf": {"target": "Package", "version": "[2.0.1, )"}, "Microsoft.Xaml.Behaviors.Wpf": {"target": "Package", "version": "[1.1.135, )"}, "System.Management": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}}