# 🎮 Roblox Anti-AFK Manager

A visually stunning Roblox Anti-AFK Manager with Apple-style "Liquid Glass" UI that prevents you from getting kicked for being idle.

## ✨ Features

### 🔍 Roblox Process Detection
- Automatically detects all running Roblox game instances
- Shows detailed information for each instance (PID, window title, start time)
- Real-time validation of process status

### 🛡️ Anti-AFK Protection
- Safe input simulation that respects Roblox's Terms of Service
- Configurable intervals (60-600 seconds)
- Random interval option to avoid detection patterns
- Per-instance protection control

### 🎨 Liquid Glass UI
- Apple-inspired frosted translucent design
- Smooth animations and transitions
- Hover effects and visual feedback
- Modern, clean, and interactive interface
- Custom glass-style buttons and cards

### 🔧 Advanced Features
- System tray integration
- Auto-refresh functionality
- Customizable settings
- Real-time status monitoring
- Minimize to tray option

## 🚀 Getting Started

### Prerequisites
- .NET 8.0 or later
- Windows 10/11
- Roblox installed

### Building the Application
```bash
dotnet build
```

### Running the Application
```bash
dotnet run
```

## 🎯 How to Use

1. **Launch the Application**: Run the executable or use `dotnet run`
2. **Start Roblox**: Launch Roblox and join any game
3. **Refresh Instances**: Click the "🔄 Refresh" button to detect Roblox instances
4. **Enable Protection**: Click "🔓 Protect" on any instance you want to keep active
5. **Configure Settings**: Expand the settings panel to customize intervals and behavior

## ⚙️ Configuration

### Anti-AFK Settings
- **Interval**: Time between anti-AFK activities (60-600 seconds)
- **Random Intervals**: Adds randomization to avoid detection patterns

### Application Settings
- **Auto-refresh**: Automatically scan for new Roblox instances
- **Refresh Interval**: How often to scan for instances (5-60 seconds)
- **Minimize to Tray**: Hide to system tray instead of closing

## 🛡️ Safety & Compliance

This application is designed to be:
- **TOS Compliant**: Uses minimal, safe input simulation
- **Anti-Cheat Safe**: Avoids suspicious patterns and excessive CPU usage
- **Non-Intrusive**: Only sends basic key presses, no memory manipulation
- **Respectful**: Minimal impact on game performance

## 🎨 UI Design Philosophy

The "Liquid Glass" aesthetic is inspired by Apple's design language:
- **Translucency**: Frosted glass effects with subtle transparency
- **Depth**: Layered shadows and gradients create visual hierarchy
- **Motion**: Smooth animations enhance user experience
- **Minimalism**: Clean, uncluttered interface focuses on functionality

## 🔧 Technical Details

### Architecture
- **MVVM Pattern**: Clean separation of concerns
- **WPF Framework**: Modern Windows UI framework
- **Async/Await**: Non-blocking operations for smooth UI
- **Win32 API**: Direct Windows integration for process management

### Dependencies
- .NET 8.0 Windows Desktop
- CommunityToolkit.Mvvm
- Hardcodet.NotifyIcon.Wpf
- Microsoft.Xaml.Behaviors.Wpf
- System.Management

## 📝 License

This project is for educational purposes. Please respect Roblox's Terms of Service and use responsibly.

## 🤝 Contributing

Feel free to submit issues and enhancement requests!

---

**⚠️ Disclaimer**: Use this tool responsibly and in accordance with Roblox's Terms of Service. The developers are not responsible for any account actions taken by Roblox.
