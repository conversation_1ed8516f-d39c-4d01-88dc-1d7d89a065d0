using System;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using RobloxAntiAFK.ViewModels;
using RobloxAntiAFK.Services;

namespace RobloxAntiAFK;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    private MainViewModel _viewModel;
    private TrayService _trayService;

    public MainWindow()
    {
        try
        {
            InitializeComponent();

            // Initialize tray service first
            _trayService = new TrayService(this);
            _trayService.Initialize(TrayIcon);

            // TODO: Initialize view model later
            // _viewModel = new MainViewModel();
            // DataContext = _viewModel;

            // Handle window state changes for tray functionality
            StateChanged += MainWindow_StateChanged;
            Closing += MainWindow_Closing;

            // Show welcome notification
            Loaded += (s, e) => _trayService?.ShowNotification("Roblox Anti-AFK Manager is ready!", NotificationType.Success);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error initializing MainWindow: {ex.Message}\n\nStack Trace:\n{ex.StackTrace}",
                           "Initialization Error", MessageBoxButton.OK, MessageBoxImage.Error);
            throw;
        }
    }

    private void MainWindow_StateChanged(object sender, EventArgs e)
    {
        if (WindowState == WindowState.Minimized && _viewModel.MinimizeToTray)
        {
            _trayService.HideMainWindow();
            _trayService.ShowNotification("Application minimized to system tray", NotificationType.Info);
        }
    }

    private void MainWindow_Closing(object sender, CancelEventArgs e)
    {
        // Hide to tray instead of closing if minimize to tray is enabled
        if (_viewModel.MinimizeToTray)
        {
            e.Cancel = true;
            _trayService.HideMainWindow();
            _trayService.ShowNotification("Application is still running in the background", NotificationType.Info);
        }
        else
        {
            _viewModel?.Cleanup();
            _trayService?.Dispose();
        }
    }

    // Title bar drag functionality
    private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
    {
        if (e.ButtonState == MouseButtonState.Pressed)
        {
            DragMove();
        }
    }

    // Window control buttons
    private void MinimizeButton_Click(object sender, RoutedEventArgs e)
    {
        WindowState = WindowState.Minimized;
    }

    private void CloseButton_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }

    // Tray icon context menu handlers
    private void ShowWindow_Click(object sender, RoutedEventArgs e)
    {
        _trayService.ShowMainWindow();
    }

    private void HideWindow_Click(object sender, RoutedEventArgs e)
    {
        _trayService.HideMainWindow();
    }

    private void ExitApplication_Click(object sender, RoutedEventArgs e)
    {
        _viewModel?.Cleanup();
        _trayService?.Dispose();
        Application.Current.Shutdown();
    }

    private void ProtectButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button)
        {
            if (button.Content.ToString().Contains("Protect"))
            {
                button.Content = "🛡️ Protected";
                var brush = new LinearGradientBrush();
                brush.GradientStops.Add(new GradientStop(Color.FromArgb(0x40, 0x00, 0x7A, 0xCC), 0.0));
                brush.GradientStops.Add(new GradientStop(Color.FromArgb(0x10, 0x00, 0x7A, 0xCC), 1.0));
                button.Background = brush;
                _trayService?.ShowNotification("Protection started for Roblox instance!", NotificationType.Success);
            }
            else
            {
                button.Content = "🔓 Protect";
                button.ClearValue(Control.BackgroundProperty);
                _trayService?.ShowNotification("Protection stopped for Roblox instance!", NotificationType.Info);
            }
        }
    }

    private void RefreshButton_Click(object sender, RoutedEventArgs e)
    {
        _trayService?.ShowNotification("Scanning for Roblox instances...", NotificationType.Info);
        // TODO: Implement actual refresh logic
    }

    private void ProtectAllButton_Click(object sender, RoutedEventArgs e)
    {
        _trayService?.ShowNotification("Protection started for all instances!", NotificationType.Success);
        // TODO: Implement protect all logic
    }

    private void UnprotectAllButton_Click(object sender, RoutedEventArgs e)
    {
        _trayService?.ShowNotification("Protection stopped for all instances!", NotificationType.Info);
        // TODO: Implement unprotect all logic
    }
}