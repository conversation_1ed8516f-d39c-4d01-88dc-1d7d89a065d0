﻿using System;
using System.ComponentModel;
using System.Windows;
using System.Windows.Input;
using RobloxAntiAFK.ViewModels;

namespace RobloxAntiAFK;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    private MainViewModel _viewModel;

    public MainWindow()
    {
        InitializeComponent();
        _viewModel = new MainViewModel();
        DataContext = _viewModel;

        // Handle window state changes for tray functionality
        StateChanged += MainWindow_StateChanged;
        Closing += MainWindow_Closing;
    }

    private void MainWindow_StateChanged(object sender, EventArgs e)
    {
        if (WindowState == WindowState.Minimized && _viewModel.MinimizeToTray)
        {
            Hide();
            TrayIcon.ShowBalloonTip("Roblox Anti-AFK Manager",
                                   "Application minimized to system tray",
                                   Hardcodet.Wpf.TaskbarNotification.BalloonIcon.Info);
        }
    }

    private void MainWindow_Closing(object sender, CancelEventArgs e)
    {
        // Hide to tray instead of closing if minimize to tray is enabled
        if (_viewModel.MinimizeToTray)
        {
            e.Cancel = true;
            Hide();
            TrayIcon.ShowBalloonTip("Roblox Anti-AFK Manager",
                                   "Application is still running in the background",
                                   Hardcodet.Wpf.TaskbarNotification.BalloonIcon.Info);
        }
        else
        {
            _viewModel?.Cleanup();
        }
    }

    // Title bar drag functionality
    private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
    {
        if (e.ButtonState == MouseButtonState.Pressed)
        {
            DragMove();
        }
    }

    // Window control buttons
    private void MinimizeButton_Click(object sender, RoutedEventArgs e)
    {
        WindowState = WindowState.Minimized;
    }

    private void CloseButton_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }

    // Tray icon context menu handlers
    private void ShowWindow_Click(object sender, RoutedEventArgs e)
    {
        Show();
        WindowState = WindowState.Normal;
        Activate();
    }

    private void HideWindow_Click(object sender, RoutedEventArgs e)
    {
        Hide();
    }

    private void ExitApplication_Click(object sender, RoutedEventArgs e)
    {
        _viewModel?.Cleanup();
        Application.Current.Shutdown();
    }
}