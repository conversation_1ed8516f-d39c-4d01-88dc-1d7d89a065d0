using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Threading;
using RobloxAntiAFK.Models;

namespace RobloxAntiAFK.Services
{
    public class AntiAFKService
    {
        [DllImport("user32.dll")]
        private static extern bool SetForegroundWindow(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

        [DllImport("user32.dll")]
        private static extern void keybd_event(byte bVk, byte bScan, uint dwFlags, UIntPtr dwExtraInfo);

        [DllImport("user32.dll")]
        private static extern bool SetCursorPos(int x, int y);

        [DllImport("user32.dll")]
        private static extern bool GetWindowRect(IntPtr hWnd, out RECT lpRect);

        [DllImport("user32.dll")]
        private static extern IntPtr GetForegroundWindow();

        [StructLayout(LayoutKind.Sequential)]
        public struct RECT
        {
            public int Left;
            public int Top;
            public int Right;
            public int Bottom;
        }

        private const int SW_RESTORE = 9;
        private const byte VK_SPACE = 0x20;
        private const uint KEYEVENTF_KEYUP = 0x0002;

        private readonly Dictionary<int, Timer> _activeTimers = new();
        private readonly Dictionary<int, DateTime> _lastActivityTimes = new();
        private readonly Random _random = new();
        private readonly object _lockObject = new();

        public int IntervalSeconds { get; set; } = 300; // 5 minutes default
        public bool UseRandomInterval { get; set; } = true;
        public int MinIntervalSeconds { get; set; } = 240; // 4 minutes
        public int MaxIntervalSeconds { get; set; } = 360; // 6 minutes
        public bool EnableMouseMovement { get; set; } = true;
        public bool EnableKeyPresses { get; set; } = true;
        public int MaxDailyActivities { get; set; } = 1000; // Safety limit

        private readonly Dictionary<int, int> _dailyActivityCount = new();
        private DateTime _lastResetDate = DateTime.Today;

        public event EventHandler<RobloxInstance> ActivityPerformed;

        public void StartProtection(RobloxInstance instance)
        {
            if (instance == null || !instance.IsRunning) return;

            lock (_lockObject)
            {
                StopProtection(instance.ProcessId);

                var interval = GetNextInterval();
                var timer = new Timer(async _ => await PerformAntiAFKActivity(instance), 
                                    null, TimeSpan.FromSeconds(interval), TimeSpan.FromMilliseconds(-1));

                _activeTimers[instance.ProcessId] = timer;
                _lastActivityTimes[instance.ProcessId] = DateTime.Now;

                Debug.WriteLine($"Started AFK protection for process {instance.ProcessId} with {interval}s interval");
            }
        }

        public void StopProtection(int processId)
        {
            lock (_lockObject)
            {
                if (_activeTimers.TryGetValue(processId, out var timer))
                {
                    timer?.Dispose();
                    _activeTimers.Remove(processId);
                    _lastActivityTimes.Remove(processId);
                    Debug.WriteLine($"Stopped AFK protection for process {processId}");
                }
            }
        }

        public void StopAllProtection()
        {
            lock (_lockObject)
            {
                foreach (var timer in _activeTimers.Values)
                {
                    timer?.Dispose();
                }
                _activeTimers.Clear();
                _lastActivityTimes.Clear();
                Debug.WriteLine("Stopped all AFK protection");
            }
        }

        private async Task PerformAntiAFKActivity(RobloxInstance instance)
        {
            try
            {
                if (!instance.IsProcessValid() || !instance.IsRunning)
                {
                    StopProtection(instance.ProcessId);
                    return;
                }

                // Check daily activity limits for safety
                if (!CheckDailyActivityLimit(instance.ProcessId))
                {
                    Debug.WriteLine($"Daily activity limit reached for process {instance.ProcessId}");
                    return;
                }

                await Task.Run(() =>
                {
                    var currentForeground = GetForegroundWindow();
                    var wasMinimized = false;

                    try
                    {
                        // Gently bring window to foreground if needed
                        if (currentForeground != instance.WindowHandle)
                        {
                            ShowWindow(instance.WindowHandle, SW_RESTORE);
                            SetForegroundWindow(instance.WindowHandle);
                            wasMinimized = true;
                            Thread.Sleep(100); // Brief pause to ensure window is active
                        }

                        // Perform safe, minimal activity
                        PerformSafeActivity(instance);

                        // Increment daily activity counter
                        IncrementDailyActivityCount(instance.ProcessId);

                        // Restore previous window if we changed it
                        if (wasMinimized && currentForeground != IntPtr.Zero)
                        {
                            Thread.Sleep(50);
                            SetForegroundWindow(currentForeground);
                        }

                        // Update activity time
                        instance.LastActivity = DateTime.Now;
                        _lastActivityTimes[instance.ProcessId] = DateTime.Now;

                        // Schedule next activity
                        ScheduleNextActivity(instance);

                        ActivityPerformed?.Invoke(this, instance);
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Error performing AFK activity for process {instance.ProcessId}: {ex.Message}");
                        // Restore foreground window on error
                        if (wasMinimized && currentForeground != IntPtr.Zero)
                        {
                            SetForegroundWindow(currentForeground);
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in PerformAntiAFKActivity: {ex.Message}");
            }
        }

        private void PerformSafeActivity(RobloxInstance instance)
        {
            // Determine available activity types based on settings
            var availableActivities = new List<int>();

            if (EnableKeyPresses)
            {
                availableActivities.Add(0); // Space key
                availableActivities.Add(2); // Shift key
            }

            if (EnableMouseMovement)
            {
                availableActivities.Add(1); // Mouse movement
            }

            if (availableActivities.Count == 0)
            {
                // Fallback to safest option
                availableActivities.Add(0);
            }

            var activityType = availableActivities[_random.Next(availableActivities.Count)];

            switch (activityType)
            {
                case 0:
                    // Brief space key press (most common and safest)
                    PerformKeyPress(VK_SPACE);
                    break;
                case 1:
                    // Subtle mouse movement within window bounds
                    PerformMouseMovement(instance);
                    break;
                case 2:
                    // Brief shift key press (walking modifier)
                    PerformKeyPress(0x10); // VK_SHIFT
                    break;
            }

            Debug.WriteLine($"Performed safe AFK activity (type {activityType}) for process {instance.ProcessId}");
        }

        private void PerformKeyPress(byte keyCode)
        {
            keybd_event(keyCode, 0, 0, UIntPtr.Zero);
            Thread.Sleep(_random.Next(30, 80)); // Random hold time
            keybd_event(keyCode, 0, KEYEVENTF_KEYUP, UIntPtr.Zero);
        }

        private void PerformMouseMovement(RobloxInstance instance)
        {
            if (GetWindowRect(instance.WindowHandle, out RECT rect))
            {
                // Get current cursor position
                var centerX = (rect.Left + rect.Right) / 2;
                var centerY = (rect.Top + rect.Bottom) / 2;

                // Small random movement within window center area
                var offsetX = _random.Next(-20, 21);
                var offsetY = _random.Next(-20, 21);

                SetCursorPos(centerX + offsetX, centerY + offsetY);
                Thread.Sleep(_random.Next(50, 150));
                SetCursorPos(centerX, centerY); // Return to center
            }
        }

        private void ScheduleNextActivity(RobloxInstance instance)
        {
            lock (_lockObject)
            {
                if (_activeTimers.TryGetValue(instance.ProcessId, out var currentTimer))
                {
                    currentTimer.Dispose();
                    
                    var interval = GetNextInterval();
                    var newTimer = new Timer(async _ => await PerformAntiAFKActivity(instance),
                                           null, TimeSpan.FromSeconds(interval), TimeSpan.FromMilliseconds(-1));
                    
                    _activeTimers[instance.ProcessId] = newTimer;
                }
            }
        }

        private int GetNextInterval()
        {
            if (UseRandomInterval)
            {
                return _random.Next(MinIntervalSeconds, MaxIntervalSeconds + 1);
            }
            return IntervalSeconds;
        }

        public DateTime? GetLastActivityTime(int processId)
        {
            return _lastActivityTimes.TryGetValue(processId, out var time) ? time : null;
        }

        public bool IsProtectionActive(int processId)
        {
            return _activeTimers.ContainsKey(processId);
        }

        private bool CheckDailyActivityLimit(int processId)
        {
            ResetDailyCountersIfNeeded();

            if (_dailyActivityCount.TryGetValue(processId, out var count))
            {
                return count < MaxDailyActivities;
            }
            return true;
        }

        private void IncrementDailyActivityCount(int processId)
        {
            ResetDailyCountersIfNeeded();

            if (_dailyActivityCount.ContainsKey(processId))
            {
                _dailyActivityCount[processId]++;
            }
            else
            {
                _dailyActivityCount[processId] = 1;
            }
        }

        private void ResetDailyCountersIfNeeded()
        {
            if (DateTime.Today > _lastResetDate)
            {
                _dailyActivityCount.Clear();
                _lastResetDate = DateTime.Today;
                Debug.WriteLine("Daily activity counters reset");
            }
        }

        public Dictionary<int, int> GetDailyActivityCounts()
        {
            ResetDailyCountersIfNeeded();
            return new Dictionary<int, int>(_dailyActivityCount);
        }
    }
}
