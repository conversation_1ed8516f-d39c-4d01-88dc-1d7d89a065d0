using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows.Threading;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using RobloxAntiAFK.Models;
using RobloxAntiAFK.Services;

namespace RobloxAntiAFK.ViewModels
{
    public partial class MainViewModel : ObservableObject
    {
        private readonly RobloxProcessService _processService;
        private readonly AntiAFKService _antiAFKService;
        private readonly LoggingService _loggingService;
        private readonly DispatcherTimer _refreshTimer;

        [ObservableProperty]
        private ObservableCollection<RobloxInstance> _robloxInstances;

        [ObservableProperty]
        private bool _isScanning;

        [ObservableProperty]
        private string _statusMessage;

        [ObservableProperty]
        private int _protectedCount;

        [ObservableProperty]
        private int _totalCount;

        [ObservableProperty]
        private bool _autoRefresh;

        [ObservableProperty]
        private int _refreshInterval;

        [ObservableProperty]
        private int _afkInterval;

        [ObservableProperty]
        private bool _useRandomInterval;

        [ObservableProperty]
        private bool _minimizeToTray;

        [ObservableProperty]
        private StatusDashboardViewModel _statusDashboard;

        public MainViewModel()
        {
            _processService = new RobloxProcessService();
            _antiAFKService = new AntiAFKService();
            _loggingService = new LoggingService();

            RobloxInstances = new ObservableCollection<RobloxInstance>();
            StatusDashboard = new StatusDashboardViewModel(_loggingService);
            
            // Initialize settings
            AutoRefresh = true;
            RefreshInterval = 10; // seconds
            AfkInterval = 300; // 5 minutes
            UseRandomInterval = true;
            MinimizeToTray = true;

            // Setup refresh timer
            _refreshTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(RefreshInterval)
            };
            _refreshTimer.Tick += async (s, e) => await RefreshInstancesAsync();

            // Subscribe to AFK service events
            _antiAFKService.ActivityPerformed += OnActivityPerformed;

            StatusMessage = "Ready to scan for Roblox instances";
            _loggingService.LogInfo("Roblox Anti-AFK Manager started");

            // Start initial scan
            _ = Task.Run(async () => await RefreshInstancesAsync());
        }

        [RelayCommand]
        private async Task RefreshInstancesAsync()
        {
            if (IsScanning) return;

            IsScanning = true;
            StatusMessage = "Scanning for Roblox instances...";

            try
            {
                var instances = await _processService.GetRobloxInstancesAsync();
                
                // Update existing instances or add new ones
                var currentIds = RobloxInstances.Select(r => r.ProcessId).ToHashSet();
                var newIds = instances.Select(r => r.ProcessId).ToHashSet();

                // Remove instances that are no longer running
                var toRemove = RobloxInstances.Where(r => !newIds.Contains(r.ProcessId)).ToList();
                foreach (var instance in toRemove)
                {
                    if (instance.IsProtected)
                    {
                        _antiAFKService.StopProtection(instance.ProcessId);
                    }
                    RobloxInstances.Remove(instance);
                }

                // Add new instances
                foreach (var instance in instances.Where(i => !currentIds.Contains(i.ProcessId)))
                {
                    RobloxInstances.Add(instance);
                }

                // Validate existing instances
                foreach (var instance in RobloxInstances.ToList())
                {
                    await _processService.ValidateInstanceAsync(instance);
                    if (!instance.IsRunning && instance.IsProtected)
                    {
                        _antiAFKService.StopProtection(instance.ProcessId);
                        instance.IsProtected = false;
                    }
                }

                UpdateCounts();
                StatusMessage = $"Found {RobloxInstances.Count} Roblox instance(s)";
                _loggingService.LogInfo($"Scan completed: {RobloxInstances.Count} instance(s) found");

                // Update status dashboard
                StatusDashboard.UpdateProcessStatistics(RobloxInstances);
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error scanning: {ex.Message}";
                _loggingService.LogError("Error during instance scan", ex);
            }
            finally
            {
                IsScanning = false;
            }
        }

        [RelayCommand]
        private void ToggleProtection(RobloxInstance instance)
        {
            if (instance == null) return;

            if (instance.IsProtected)
            {
                _antiAFKService.StopProtection(instance.ProcessId);
                instance.IsProtected = false;
                StatusMessage = $"Stopped protection for {instance.DisplayName}";
                _loggingService.LogActivity($"Protection stopped", instance);
            }
            else
            {
                if (instance.IsRunning)
                {
                    _antiAFKService.IntervalSeconds = AfkInterval;
                    _antiAFKService.UseRandomInterval = UseRandomInterval;
                    _antiAFKService.StartProtection(instance);
                    instance.IsProtected = true;
                    StatusMessage = $"Started protection for {instance.DisplayName}";
                    _loggingService.LogActivity($"Protection started (interval: {AfkInterval}s)", instance);
                }
                else
                {
                    StatusMessage = $"Cannot protect {instance.DisplayName} - process not running";
                    _loggingService.LogWarning($"Cannot protect {instance.DisplayName} - process not running", instance.ProcessId);
                }
            }

            UpdateCounts();
        }

        [RelayCommand]
        private void ProtectAll()
        {
            foreach (var instance in RobloxInstances.Where(r => r.IsRunning && !r.IsProtected))
            {
                ToggleProtection(instance);
            }
        }

        [RelayCommand]
        private void UnprotectAll()
        {
            foreach (var instance in RobloxInstances.Where(r => r.IsProtected))
            {
                ToggleProtection(instance);
            }
        }

        [RelayCommand]
        private void StartAutoRefresh()
        {
            if (AutoRefresh && !_refreshTimer.IsEnabled)
            {
                _refreshTimer.Interval = TimeSpan.FromSeconds(RefreshInterval);
                _refreshTimer.Start();
                StatusMessage = "Auto-refresh enabled";
            }
            else if (!AutoRefresh && _refreshTimer.IsEnabled)
            {
                _refreshTimer.Stop();
                StatusMessage = "Auto-refresh disabled";
            }
        }

        partial void OnAutoRefreshChanged(bool value)
        {
            StartAutoRefresh();
        }

        partial void OnRefreshIntervalChanged(int value)
        {
            if (_refreshTimer != null)
            {
                _refreshTimer.Interval = TimeSpan.FromSeconds(Math.Max(5, value));
            }
        }

        partial void OnAfkIntervalChanged(int value)
        {
            _antiAFKService.IntervalSeconds = Math.Max(60, value);
        }

        partial void OnUseRandomIntervalChanged(bool value)
        {
            _antiAFKService.UseRandomInterval = value;
        }

        private void OnActivityPerformed(object sender, RobloxInstance instance)
        {
            App.Current?.Dispatcher.Invoke(() =>
            {
                StatusMessage = $"Activity performed for {instance.DisplayName}";
                _loggingService.LogActivity("Anti-AFK activity performed", instance);
            });
        }

        private void UpdateCounts()
        {
            TotalCount = RobloxInstances.Count;
            ProtectedCount = RobloxInstances.Count(r => r.IsProtected);
        }

        public void Cleanup()
        {
            _loggingService.LogInfo("Shutting down Roblox Anti-AFK Manager");
            _refreshTimer?.Stop();
            _antiAFKService?.StopAllProtection();
            StatusDashboard?.Cleanup();
        }
    }
}
