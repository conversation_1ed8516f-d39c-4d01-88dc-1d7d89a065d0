using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using RobloxAntiAFK.Models;

namespace RobloxAntiAFK.Services
{
    public class LoggingService
    {
        private readonly string _logDirectory;
        private readonly string _logFilePath;
        private readonly List<LogEntry> _recentLogs;
        private readonly object _lockObject = new();

        public bool IsLoggingEnabled { get; set; } = true;
        public int MaxRecentLogs { get; set; } = 1000;

        public event EventHandler<LogEntry> LogEntryAdded;

        public LoggingService()
        {
            _logDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "RobloxAntiAFK");
            _logFilePath = Path.Combine(_logDirectory, $"log_{DateTime.Now:yyyy-MM-dd}.txt");
            _recentLogs = new List<LogEntry>();

            // Ensure log directory exists
            Directory.CreateDirectory(_logDirectory);
        }

        public void LogInfo(string message, int? processId = null)
        {
            Log(LogLevel.Info, message, processId);
        }

        public void LogWarning(string message, int? processId = null)
        {
            Log(LogLevel.Warning, message, processId);
        }

        public void LogError(string message, Exception exception = null, int? processId = null)
        {
            var fullMessage = exception != null ? $"{message}: {exception.Message}" : message;
            Log(LogLevel.Error, fullMessage, processId);
        }

        public void LogActivity(string activity, RobloxInstance instance)
        {
            Log(LogLevel.Activity, $"[{instance.DisplayName}] {activity}", instance.ProcessId);
        }

        private void Log(LogLevel level, string message, int? processId = null)
        {
            if (!IsLoggingEnabled) return;

            var logEntry = new LogEntry
            {
                Timestamp = DateTime.Now,
                Level = level,
                Message = message,
                ProcessId = processId
            };

            lock (_lockObject)
            {
                // Add to recent logs
                _recentLogs.Add(logEntry);
                
                // Maintain max recent logs limit
                if (_recentLogs.Count > MaxRecentLogs)
                {
                    _recentLogs.RemoveAt(0);
                }
            }

            // Write to file asynchronously
            _ = Task.Run(() => WriteToFile(logEntry));

            // Notify subscribers
            LogEntryAdded?.Invoke(this, logEntry);
        }

        private async Task WriteToFile(LogEntry entry)
        {
            try
            {
                var logLine = FormatLogEntry(entry);
                await File.AppendAllTextAsync(_logFilePath, logLine + Environment.NewLine);
            }
            catch (Exception ex)
            {
                // Avoid infinite recursion by not logging this error
                System.Diagnostics.Debug.WriteLine($"Failed to write log entry: {ex.Message}");
            }
        }

        private string FormatLogEntry(LogEntry entry)
        {
            var processInfo = entry.ProcessId.HasValue ? $" [PID:{entry.ProcessId}]" : "";
            return $"[{entry.Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{entry.Level}]{processInfo} {entry.Message}";
        }

        public List<LogEntry> GetRecentLogs(int count = 100)
        {
            lock (_lockObject)
            {
                return _recentLogs.TakeLast(count).ToList();
            }
        }

        public List<LogEntry> GetLogsForProcess(int processId, int count = 50)
        {
            lock (_lockObject)
            {
                return _recentLogs
                    .Where(log => log.ProcessId == processId)
                    .TakeLast(count)
                    .ToList();
            }
        }

        public void ClearLogs()
        {
            lock (_lockObject)
            {
                _recentLogs.Clear();
            }
        }

        public async Task<string> ExportLogsAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var exportPath = Path.Combine(_logDirectory, $"export_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}.txt");
            
            var logsToExport = _recentLogs.AsEnumerable();
            
            if (fromDate.HasValue)
                logsToExport = logsToExport.Where(log => log.Timestamp >= fromDate.Value);
                
            if (toDate.HasValue)
                logsToExport = logsToExport.Where(log => log.Timestamp <= toDate.Value);

            var exportLines = logsToExport.Select(FormatLogEntry);
            await File.WriteAllLinesAsync(exportPath, exportLines);
            
            return exportPath;
        }
    }

    public class LogEntry
    {
        public DateTime Timestamp { get; set; }
        public LogLevel Level { get; set; }
        public string Message { get; set; }
        public int? ProcessId { get; set; }

        public string LevelIcon => Level switch
        {
            LogLevel.Info => "ℹ️",
            LogLevel.Warning => "⚠️",
            LogLevel.Error => "❌",
            LogLevel.Activity => "🎯",
            _ => "📝"
        };

        public string FormattedMessage => $"{LevelIcon} [{Timestamp:HH:mm:ss}] {Message}";
    }

    public enum LogLevel
    {
        Info,
        Warning,
        Error,
        Activity
    }
}
