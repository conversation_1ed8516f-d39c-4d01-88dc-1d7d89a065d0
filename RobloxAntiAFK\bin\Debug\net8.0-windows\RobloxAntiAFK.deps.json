{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"RobloxAntiAFK/1.0.0": {"dependencies": {"CommunityToolkit.Mvvm": "8.4.0", "Hardcodet.NotifyIcon.Wpf": "2.0.1", "Microsoft.Xaml.Behaviors.Wpf": "1.1.135", "System.Management": "9.0.6"}, "runtime": {"RobloxAntiAFK.dll": {}}}, "CommunityToolkit.Mvvm/8.4.0": {"runtime": {"lib/net8.0/CommunityToolkit.Mvvm.dll": {"assemblyVersion": "8.4.0.0", "fileVersion": "8.4.0.1"}}}, "Hardcodet.NotifyIcon.Wpf/2.0.1": {"runtime": {"lib/net8.0-windows7.0/Hardcodet.NotifyIcon.Wpf.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.1.2717"}}}, "Microsoft.Xaml.Behaviors.Wpf/1.1.135": {"runtime": {"lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.1.135.29210"}}}, "System.CodeDom/9.0.6": {"runtime": {"lib/net8.0/System.CodeDom.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26613"}}}, "System.Management/9.0.6": {"dependencies": {"System.CodeDom": "9.0.6"}, "runtime": {"lib/net8.0/System.Management.dll": {"assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}}}}}, "libraries": {"RobloxAntiAFK/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "CommunityToolkit.Mvvm/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-tqVU8yc/ADO9oiTRyTnwhFN68hCwvkliMierptWOudIAvWY1mWCh5VFh+guwHJmpMwfg0J0rY+yyd5Oy7ty9Uw==", "path": "communitytoolkit.mvvm/8.4.0", "hashPath": "communitytoolkit.mvvm.8.4.0.nupkg.sha512"}, "Hardcodet.NotifyIcon.Wpf/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-dtxmeZXzV2GzSm91aZ3hqzgoeVoARSkDPVCYfhVUNyyKBWYxMgNC0EcLiSYxD4Uc4alq/2qb3SmV8DgAENLRLQ==", "path": "hardcodet.notifyicon.wpf/2.0.1", "hashPath": "hardcodet.notifyicon.wpf.2.0.1.nupkg.sha512"}, "Microsoft.Xaml.Behaviors.Wpf/1.1.135": {"type": "package", "serviceable": true, "sha512": "sha512-r8qBEXmQfORso2+MVHnt8PSH4761zJ0SIxgQTSEDVLU97EN2FZdG6/ZCYUPhQy+OrPKgnpYBCAs3PS6Bs7wRsg==", "path": "microsoft.xaml.behaviors.wpf/1.1.135", "hashPath": "microsoft.xaml.behaviors.wpf.1.1.135.nupkg.sha512"}, "System.CodeDom/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-9u1pyEykc0bWBHf1cIVwRoMqrEtxtXdC2ss1K02pvrkwHPcyYmy1glO+kLbyqPO9ehCTl+2dFyUuTUNl1Fde5g==", "path": "system.codedom/9.0.6", "hashPath": "system.codedom.9.0.6.nupkg.sha512"}, "System.Management/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-pOc5bCurWL3qVDsPP0iycUCRfXBhI/fVe44SiAlVqoZDIbHP080CLyCzfXV1UbdGKN0hQSLSHqr7OI3BhLBRbA==", "path": "system.management/9.0.6", "hashPath": "system.management.9.0.6.nupkg.sha512"}}}