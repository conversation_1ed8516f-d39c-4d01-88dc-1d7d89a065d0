<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Color Palette -->
    <Color x:Key="GlassBaseColor">#1A1A1A</Color>
    <Color x:Key="GlassAccentColor">#2D2D30</Color>
    <Color x:Key="GlassHighlightColor">#404040</Color>
    <Color x:Key="GlassBorderColor">#555555</Color>
    <Color x:Key="AccentBlue">#007ACC</Color>
    <Color x:Key="AccentGreen">#16C60C</Color>
    <Color x:Key="AccentRed">#E74856</Color>
    <Color x:Key="AccentOrange">#FF8C00</Color>

    <!-- Brushes -->
    <SolidColorBrush x:Key="GlassBaseBrush" Color="{StaticResource GlassBaseColor}"/>
    <SolidColorBrush x:Key="GlassAccentBrush" Color="{StaticResource GlassAccentColor}"/>
    <SolidColorBrush x:Key="GlassHighlightBrush" Color="{StaticResource GlassHighlightColor}"/>
    <SolidColorBrush x:Key="GlassBorderBrush" Color="{StaticResource GlassBorderColor}"/>
    <SolidColorBrush x:Key="AccentBlueBrush" Color="{StaticResource AccentBlue}"/>
    <SolidColorBrush x:Key="AccentGreenBrush" Color="{StaticResource AccentGreen}"/>
    <SolidColorBrush x:Key="AccentRedBrush" Color="{StaticResource AccentRed}"/>
    <SolidColorBrush x:Key="AccentOrangeBrush" Color="{StaticResource AccentOrange}"/>

    <!-- Glass Effect Brushes -->
    <LinearGradientBrush x:Key="GlassGradientBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#40FFFFFF" Offset="0"/>
        <GradientStop Color="#20FFFFFF" Offset="0.5"/>
        <GradientStop Color="#10FFFFFF" Offset="1"/>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="GlassCardBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#30FFFFFF" Offset="0"/>
        <GradientStop Color="#15FFFFFF" Offset="0.5"/>
        <GradientStop Color="#08FFFFFF" Offset="1"/>
    </LinearGradientBrush>

    <!-- Animations -->
    <Storyboard x:Key="FadeInAnimation">
        <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0" To="1" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <Storyboard x:Key="ScaleUpAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)" 
                         From="0.95" To="1" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)" 
                         From="0.95" To="1" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- Glass Button Style -->
    <Style x:Key="GlassButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource GlassCardBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource GlassBorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="RenderTransform">
            <Setter.Value>
                <ScaleTransform ScaleX="1" ScaleY="1"/>
            </Setter.Value>
        </Setter>
        <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border" 
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" 
                                            BlurRadius="8" Opacity="0.3"/>
                        </Border.Effect>
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"
                                        Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Trigger.EnterActions>
                                <BeginStoryboard>
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="border"
                                                       Storyboard.TargetProperty="(Border.Background).(LinearGradientBrush.GradientStops)[0].Color.A"
                                                       To="80" Duration="0:0:0.2"/>
                                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                                       To="1.05" Duration="0:0:0.2"/>
                                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                                       To="1.05" Duration="0:0:0.2"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.EnterActions>
                            <Trigger.ExitActions>
                                <BeginStoryboard>
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="border"
                                                       Storyboard.TargetProperty="(Border.Background).(LinearGradientBrush.GradientStops)[0].Color.A"
                                                       To="48" Duration="0:0:0.2"/>
                                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                                       To="1" Duration="0:0:0.2"/>
                                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                                       To="1" Duration="0:0:0.2"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.ExitActions>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Glass Card Style -->
    <Style x:Key="GlassCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource GlassCardBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource GlassBorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="12"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" Direction="270" ShadowDepth="4" 
                                BlurRadius="12" Opacity="0.4"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Glass TextBlock Style -->
    <Style x:Key="GlassTextStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontWeight" Value="Normal"/>
    </Style>

    <!-- Glass Header Style -->
    <Style x:Key="GlassHeaderStyle" TargetType="TextBlock" BasedOn="{StaticResource GlassTextStyle}">
        <Setter Property="FontSize" Value="24"/>
        <Setter Property="FontWeight" Value="Light"/>
        <Setter Property="Margin" Value="0,0,0,16"/>
    </Style>

    <!-- Glass Subheader Style -->
    <Style x:Key="GlassSubheaderStyle" TargetType="TextBlock" BasedOn="{StaticResource GlassTextStyle}">
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Margin" Value="0,0,0,8"/>
    </Style>

</ResourceDictionary>
