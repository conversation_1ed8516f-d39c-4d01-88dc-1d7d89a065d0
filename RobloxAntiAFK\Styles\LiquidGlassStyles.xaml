<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Color Palette -->
    <Color x:Key="GlassBaseColor">#1A1A1A</Color>
    <Color x:Key="GlassAccentColor">#2D2D30</Color>
    <Color x:Key="GlassHighlightColor">#404040</Color>
    <Color x:Key="GlassBorderColor">#555555</Color>
    <Color x:Key="AccentBlue">#007ACC</Color>
    <Color x:Key="AccentGreen">#16C60C</Color>
    <Color x:Key="AccentRed">#E74856</Color>
    <Color x:Key="AccentOrange">#FF8C00</Color>

    <!-- Brushes -->
    <SolidColorBrush x:Key="GlassBaseBrush" Color="{StaticResource GlassBaseColor}"/>
    <SolidColorBrush x:Key="GlassAccentBrush" Color="{StaticResource GlassAccentColor}"/>
    <SolidColorBrush x:Key="GlassHighlightBrush" Color="{StaticResource GlassHighlightColor}"/>
    <SolidColorBrush x:Key="GlassBorderBrush" Color="{StaticResource GlassBorderColor}"/>
    <SolidColorBrush x:Key="AccentBlueBrush" Color="{StaticResource AccentBlue}"/>
    <SolidColorBrush x:Key="AccentGreenBrush" Color="{StaticResource AccentGreen}"/>
    <SolidColorBrush x:Key="AccentRedBrush" Color="{StaticResource AccentRed}"/>
    <SolidColorBrush x:Key="AccentOrangeBrush" Color="{StaticResource AccentOrange}"/>

    <!-- Glass Effect Brushes -->
    <LinearGradientBrush x:Key="GlassGradientBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#40FFFFFF" Offset="0"/>
        <GradientStop Color="#20FFFFFF" Offset="0.5"/>
        <GradientStop Color="#10FFFFFF" Offset="1"/>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="GlassCardBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#30FFFFFF" Offset="0"/>
        <GradientStop Color="#15FFFFFF" Offset="0.5"/>
        <GradientStop Color="#08FFFFFF" Offset="1"/>
    </LinearGradientBrush>

    <!-- Advanced Animations -->
    <Storyboard x:Key="FadeInAnimation">
        <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0" To="1" Duration="0:0:0.4">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <Storyboard x:Key="ScaleUpAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                         From="0.95" To="1" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                         From="0.95" To="1" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <Storyboard x:Key="PulseAnimation">
        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="Opacity" RepeatBehavior="Forever">
            <EasingDoubleKeyFrame KeyTime="0:0:0" Value="0.8"/>
            <EasingDoubleKeyFrame KeyTime="0:0:1" Value="1.0">
                <EasingDoubleKeyFrame.EasingFunction>
                    <SineEase EasingMode="EaseInOut"/>
                </EasingDoubleKeyFrame.EasingFunction>
            </EasingDoubleKeyFrame>
            <EasingDoubleKeyFrame KeyTime="0:0:2" Value="0.8">
                <EasingDoubleKeyFrame.EasingFunction>
                    <SineEase EasingMode="EaseInOut"/>
                </EasingDoubleKeyFrame.EasingFunction>
            </EasingDoubleKeyFrame>
        </DoubleAnimationUsingKeyFrames>
    </Storyboard>

    <Storyboard x:Key="GlowAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.Effect).(DropShadowEffect.BlurRadius)"
                         From="8" To="16" Duration="0:0:0.3" AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <SineEase EasingMode="EaseInOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- Glass Button Style -->
    <Style x:Key="GlassButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource GlassCardBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource GlassBorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="RenderTransform">
            <Setter.Value>
                <ScaleTransform ScaleX="1" ScaleY="1"/>
            </Setter.Value>
        </Setter>
        <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border" 
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" 
                                            BlurRadius="8" Opacity="0.3"/>
                        </Border.Effect>
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"
                                        Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Trigger.EnterActions>
                                <BeginStoryboard>
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="border"
                                                       Storyboard.TargetProperty="(Border.Background).(LinearGradientBrush.GradientStops)[0].Color.A"
                                                       To="80" Duration="0:0:0.2"/>
                                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                                       To="1.05" Duration="0:0:0.2"/>
                                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                                       To="1.05" Duration="0:0:0.2"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.EnterActions>
                            <Trigger.ExitActions>
                                <BeginStoryboard>
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="border"
                                                       Storyboard.TargetProperty="(Border.Background).(LinearGradientBrush.GradientStops)[0].Color.A"
                                                       To="48" Duration="0:0:0.2"/>
                                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                                       To="1" Duration="0:0:0.2"/>
                                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                                       To="1" Duration="0:0:0.2"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.ExitActions>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Glass Card Style -->
    <Style x:Key="GlassCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource GlassCardBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource GlassBorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="12"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" Direction="270" ShadowDepth="4" 
                                BlurRadius="12" Opacity="0.4"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Glass TextBlock Style -->
    <Style x:Key="GlassTextStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontWeight" Value="Normal"/>
    </Style>

    <!-- Glass Header Style -->
    <Style x:Key="GlassHeaderStyle" TargetType="TextBlock" BasedOn="{StaticResource GlassTextStyle}">
        <Setter Property="FontSize" Value="24"/>
        <Setter Property="FontWeight" Value="Light"/>
        <Setter Property="Margin" Value="0,0,0,16"/>
    </Style>

    <!-- Glass Subheader Style -->
    <Style x:Key="GlassSubheaderStyle" TargetType="TextBlock" BasedOn="{StaticResource GlassTextStyle}">
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Margin" Value="0,0,0,8"/>
    </Style>

    <!-- Glass Checkbox Style -->
    <Style x:Key="GlassCheckBoxStyle" TargetType="CheckBox">
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Margin" Value="0,4"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="CheckBox">
                    <StackPanel Orientation="Horizontal">
                        <Border x:Name="CheckBoxBorder" Width="18" Height="18"
                                Background="{StaticResource GlassCardBrush}"
                                BorderBrush="{StaticResource GlassBorderBrush}"
                                BorderThickness="1" CornerRadius="4" Margin="0,0,8,0">
                            <Border.Effect>
                                <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2"
                                                BlurRadius="4" Opacity="0.3"/>
                            </Border.Effect>
                            <TextBlock x:Name="CheckMark" Text="✓" Foreground="{StaticResource AccentBlueBrush}"
                                       HorizontalAlignment="Center" VerticalAlignment="Center"
                                       FontSize="12" FontWeight="Bold" Visibility="Collapsed"/>
                        </Border>
                        <ContentPresenter VerticalAlignment="Center"/>
                    </StackPanel>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="CheckMark" Property="Visibility" Value="Visible"/>
                            <Setter TargetName="CheckBoxBorder" Property="BorderBrush" Value="{StaticResource AccentBlueBrush}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="CheckBoxBorder" Property="BorderBrush" Value="{StaticResource AccentBlueBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Glass Slider Style -->
    <Style x:Key="GlassSliderStyle" TargetType="Slider">
        <Setter Property="Height" Value="20"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Slider">
                    <Grid>
                        <Border x:Name="TrackBackground" Height="4"
                                Background="{StaticResource GlassAccentBrush}"
                                BorderBrush="{StaticResource GlassBorderBrush}"
                                BorderThickness="1" CornerRadius="2"
                                VerticalAlignment="Center"/>
                        <Track x:Name="PART_Track">
                            <Track.DecreaseRepeatButton>
                                <RepeatButton Style="{x:Null}" Background="Transparent" Focusable="False"/>
                            </Track.DecreaseRepeatButton>
                            <Track.IncreaseRepeatButton>
                                <RepeatButton Style="{x:Null}" Background="Transparent" Focusable="False"/>
                            </Track.IncreaseRepeatButton>
                            <Track.Thumb>
                                <Thumb Width="16" Height="16">
                                    <Thumb.Template>
                                        <ControlTemplate TargetType="Thumb">
                                            <Border Background="{StaticResource AccentBlueBrush}"
                                                    BorderBrush="White" BorderThickness="2"
                                                    CornerRadius="8">
                                                <Border.Effect>
                                                    <DropShadowEffect Color="Black" Direction="270"
                                                                    ShadowDepth="2" BlurRadius="6" Opacity="0.4"/>
                                                </Border.Effect>
                                            </Border>
                                        </ControlTemplate>
                                    </Thumb.Template>
                                </Thumb>
                            </Track.Thumb>
                        </Track>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Glass Expander Style -->
    <Style x:Key="GlassExpanderStyle" TargetType="Expander">
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Expander">
                    <StackPanel>
                        <ToggleButton x:Name="HeaderSite" IsChecked="{Binding IsExpanded, RelativeSource={RelativeSource TemplatedParent}}"
                                      Background="Transparent" BorderThickness="0" HorizontalAlignment="Stretch"
                                      Padding="8,4">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="▶" x:Name="Arrow" FontSize="10" VerticalAlignment="Center"
                                           Margin="0,0,8,0" RenderTransformOrigin="0.5,0.5">
                                    <TextBlock.RenderTransform>
                                        <RotateTransform Angle="0"/>
                                    </TextBlock.RenderTransform>
                                </TextBlock>
                                <ContentPresenter ContentSource="Header" VerticalAlignment="Center"/>
                            </StackPanel>
                        </ToggleButton>
                        <ContentPresenter x:Name="ExpandSite" Visibility="Collapsed"/>
                    </StackPanel>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsExpanded" Value="True">
                            <Setter TargetName="ExpandSite" Property="Visibility" Value="Visible"/>
                            <Trigger.EnterActions>
                                <BeginStoryboard>
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="Arrow"
                                                       Storyboard.TargetProperty="(UIElement.RenderTransform).(RotateTransform.Angle)"
                                                       To="90" Duration="0:0:0.2"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.EnterActions>
                            <Trigger.ExitActions>
                                <BeginStoryboard>
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="Arrow"
                                                       Storyboard.TargetProperty="(UIElement.RenderTransform).(RotateTransform.Angle)"
                                                       To="0" Duration="0:0:0.2"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.ExitActions>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
