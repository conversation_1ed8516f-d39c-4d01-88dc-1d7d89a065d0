{"version": 2, "dgSpecHash": "YsfUcZGTBWY=", "success": true, "projectFilePath": "Z:\\rblxantiafk\\RobloxAntiAFK\\RobloxAntiAFK.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\communitytoolkit.mvvm\\8.4.0\\communitytoolkit.mvvm.8.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\hardcodet.notifyicon.wpf\\2.0.1\\hardcodet.notifyicon.wpf.2.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.xaml.behaviors.wpf\\1.1.135\\microsoft.xaml.behaviors.wpf.1.1.135.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.codedom\\9.0.6\\system.codedom.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.management\\9.0.6\\system.management.9.0.6.nupkg.sha512"], "logs": []}