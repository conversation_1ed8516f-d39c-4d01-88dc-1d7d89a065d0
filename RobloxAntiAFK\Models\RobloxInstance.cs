using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Runtime.CompilerServices;

namespace RobloxAntiAFK.Models
{
    public class RobloxInstance : INotifyPropertyChanged
    {
        private bool _isProtected;
        private DateTime _lastActivity;
        private string _status;
        private bool _isRunning;

        public int ProcessId { get; set; }
        public string ProcessName { get; set; }
        public string WindowTitle { get; set; }
        public IntPtr WindowHandle { get; set; }
        public DateTime StartTime { get; set; }

        public bool IsProtected
        {
            get => _isProtected;
            set
            {
                _isProtected = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(StatusText));
            }
        }

        public DateTime LastActivity
        {
            get => _lastActivity;
            set
            {
                _lastActivity = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(TimeSinceLastActivity));
            }
        }

        public string Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(StatusText));
            }
        }

        public bool IsRunning
        {
            get => _isRunning;
            set
            {
                _isRunning = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(StatusText));
            }
        }

        public string StatusText
        {
            get
            {
                if (!IsRunning) return "❌ Not Running";
                if (IsProtected) return "🛡️ Protected";
                return "⚠️ Unprotected";
            }
        }

        public TimeSpan TimeSinceLastActivity => DateTime.Now - LastActivity;

        public string DisplayName => string.IsNullOrEmpty(WindowTitle) ? ProcessName : WindowTitle;

        public RobloxInstance()
        {
            _lastActivity = DateTime.Now;
            _status = "Idle";
            _isRunning = true;
        }

        public bool IsProcessValid()
        {
            try
            {
                var process = Process.GetProcessById(ProcessId);
                return !process.HasExited;
            }
            catch
            {
                return false;
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
