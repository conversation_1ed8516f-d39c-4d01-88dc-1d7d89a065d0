using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using RobloxAntiAFK.Models;

namespace RobloxAntiAFK.Services
{
    public class RobloxProcessService
    {
        [DllImport("user32.dll")]
        private static extern int GetWindowText(IntPtr hWnd, StringBuilder text, int count);

        [DllImport("user32.dll")]
        private static extern bool IsWindowVisible(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern IntPtr GetWindow(IntPtr hWnd, uint uCmd);

        private const uint GW_OWNER = 4;

        public async Task<List<RobloxInstance>> GetRobloxInstancesAsync()
        {
            return await Task.Run(() =>
            {
                var instances = new List<RobloxInstance>();
                var processes = Process.GetProcessesByName("RobloxPlayerBeta");

                foreach (var process in processes)
                {
                    try
                    {
                        if (process.HasExited) continue;

                        var windowTitle = GetWindowTitle(process.MainWindowHandle);
                        
                        // Only include processes with visible windows
                        if (process.MainWindowHandle != IntPtr.Zero && 
                            IsWindowVisible(process.MainWindowHandle) &&
                            GetWindow(process.MainWindowHandle, GW_OWNER) == IntPtr.Zero)
                        {
                            var instance = new RobloxInstance
                            {
                                ProcessId = process.Id,
                                ProcessName = process.ProcessName,
                                WindowTitle = windowTitle,
                                WindowHandle = process.MainWindowHandle,
                                StartTime = process.StartTime,
                                IsRunning = true
                            };

                            instances.Add(instance);
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log error but continue processing other instances
                        Debug.WriteLine($"Error processing Roblox instance {process.Id}: {ex.Message}");
                    }
                }

                return instances;
            });
        }

        private string GetWindowTitle(IntPtr windowHandle)
        {
            if (windowHandle == IntPtr.Zero) return string.Empty;

            var buffer = new StringBuilder(256);
            GetWindowText(windowHandle, buffer, buffer.Capacity);
            return buffer.ToString();
        }

        public bool IsRobloxProcessRunning(int processId)
        {
            try
            {
                var process = Process.GetProcessById(processId);
                return !process.HasExited && process.ProcessName.Equals("RobloxPlayerBeta", StringComparison.OrdinalIgnoreCase);
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> ValidateInstanceAsync(RobloxInstance instance)
        {
            return await Task.Run(() =>
            {
                if (instance == null) return false;
                
                try
                {
                    var process = Process.GetProcessById(instance.ProcessId);
                    var isValid = !process.HasExited && 
                                 process.ProcessName.Equals("RobloxPlayerBeta", StringComparison.OrdinalIgnoreCase) &&
                                 process.MainWindowHandle != IntPtr.Zero &&
                                 IsWindowVisible(process.MainWindowHandle);

                    instance.IsRunning = isValid;
                    return isValid;
                }
                catch
                {
                    instance.IsRunning = false;
                    return false;
                }
            });
        }
    }
}
