using System;
using System.Windows;
using Hardcodet.Wpf.TaskbarNotification;

namespace RobloxAntiAFK.Services
{
    public class TrayService
    {
        private TaskbarIcon _trayIcon;
        private Window _mainWindow;

        public TrayService(Window mainWindow)
        {
            _mainWindow = mainWindow;
        }

        public void Initialize(TaskbarIcon trayIcon)
        {
            _trayIcon = trayIcon;
            
            // Set up tray icon click behavior
            _trayIcon.TrayLeftMouseUp += OnTrayIconClick;
            _trayIcon.TrayRightMouseUp += OnTrayIconRightClick;
        }

        private void OnTrayIconClick(object sender, RoutedEventArgs e)
        {
            ShowMainWindow();
        }

        private void OnTrayIconRightClick(object sender, RoutedEventArgs e)
        {
            // Context menu is handled by XAML
        }

        public void ShowMainWindow()
        {
            if (_mainWindow != null)
            {
                _mainWindow.Show();
                _mainWindow.WindowState = WindowState.Normal;
                _mainWindow.Activate();
                _mainWindow.Focus();
            }
        }

        public void HideMainWindow()
        {
            _mainWindow?.Hide();
        }

        public void ShowBalloonTip(string title, string message, BalloonIcon icon = BalloonIcon.Info, int timeout = 3000)
        {
            _trayIcon?.ShowBalloonTip(title, message, icon);
        }

        public void ShowNotification(string message, NotificationType type = NotificationType.Info)
        {
            var icon = type switch
            {
                NotificationType.Success => BalloonIcon.Info,
                NotificationType.Warning => BalloonIcon.Warning,
                NotificationType.Error => BalloonIcon.Error,
                _ => BalloonIcon.Info
            };

            var title = type switch
            {
                NotificationType.Success => "✅ Success",
                NotificationType.Warning => "⚠️ Warning", 
                NotificationType.Error => "❌ Error",
                _ => "ℹ️ Info"
            };

            ShowBalloonTip(title, message, icon);
        }

        public void Dispose()
        {
            _trayIcon?.Dispose();
        }
    }

    public enum NotificationType
    {
        Info,
        Success,
        Warning,
        Error
    }
}
