﻿<Window x:Class="RobloxAntiAFK.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:RobloxAntiAFK"
        xmlns:controls="clr-namespace:RobloxAntiAFK.Controls"
        xmlns:converters="clr-namespace:RobloxAntiAFK.Converters"
        xmlns:tb="http://www.hardcodet.net/taskbar"
        mc:Ignorable="d"
        Title="Roblox Anti-AFK Manager" Height="700" Width="1000" MinHeight="500" MinWidth="800"
        WindowStyle="None" AllowsTransparency="True" Background="Transparent"
        WindowStartupLocation="CenterScreen"
        UseLayoutRounding="True" SnapsToDevicePixels="True"
        RenderOptions.BitmapScalingMode="HighQuality"
        RenderOptions.EdgeMode="Aliased"
        TextOptions.TextFormattingMode="Display"
        TextOptions.TextRenderingMode="ClearType">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Styles/LiquidGlassStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <converters:BooleanToStringConverter x:Key="BooleanToStringConverter"/>
            <converters:CountToVisibilityConverter x:Key="CountToVisibilityConverter"/>
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        </ResourceDictionary>
    </Window.Resources>

    <Grid>
        <!-- System Tray Icon -->
        <tb:TaskbarIcon x:Name="TrayIcon"
                        ToolTipText="Roblox Anti-AFK Manager"
                        Visibility="Visible">
            <tb:TaskbarIcon.ContextMenu>
                <ContextMenu>
                    <MenuItem Header="Show" Click="ShowWindow_Click"/>
                    <MenuItem Header="Hide" Click="HideWindow_Click"/>
                    <Separator/>
                    <MenuItem Header="Exit" Click="ExitApplication_Click"/>
                </ContextMenu>
            </tb:TaskbarIcon.ContextMenu>
        </tb:TaskbarIcon>

        <!-- Main Window Content -->
        <Border Background="#E6000000" CornerRadius="16">
        <Border.Effect>
            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="8"
                            BlurRadius="24" Opacity="0.6"/>
        </Border.Effect>

        <Grid Margin="2">
            <!-- Background Gradient -->
            <Border CornerRadius="14">
                <Border.Background>
                    <RadialGradientBrush>
                        <GradientStop Color="#FF1A1A1A" Offset="0"/>
                        <GradientStop Color="#FF0D1117" Offset="1"/>
                    </RadialGradientBrush>
                </Border.Background>
            </Border>

            <!-- Content -->
            <Grid Margin="24">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Title Bar -->
                <Grid Grid.Row="0" MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock Text="🎮" FontSize="24" VerticalAlignment="Center" Margin="0,0,8,0"/>
                        <TextBlock Text="Roblox Anti-AFK Manager" Style="{StaticResource GlassHeaderStyle}" Margin="0"/>
                    </StackPanel>

                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <Button Content="−" Style="{StaticResource GlassButtonStyle}"
                                Width="32" Height="32" Padding="0" Margin="4,0"
                                Click="MinimizeButton_Click" FontSize="16"/>
                        <Button Content="✕" Style="{StaticResource GlassButtonStyle}"
                                Width="32" Height="32" Padding="0" Margin="4,0"
                                Click="CloseButton_Click" FontSize="12"/>
                    </StackPanel>
                </Grid>

                <!-- Control Panel -->
                <Border Grid.Row="1" Style="{StaticResource GlassCardStyle}" Margin="0,0,0,16">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- Status Info -->
                        <StackPanel Grid.Column="0" VerticalAlignment="Center">
                            <TextBlock Text="Ready to scan for Roblox instances" Style="{StaticResource GlassTextStyle}"
                                       FontSize="14" Margin="0,0,0,4"/>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Style="{StaticResource GlassTextStyle}" FontSize="12" Opacity="0.7">
                                    <Run Text="Total: "/>
                                    <Run Text="1" FontWeight="Bold"/>
                                    <Run Text=" • Protected: "/>
                                    <Run Text="0" FontWeight="Bold" Foreground="{StaticResource AccentBlueBrush}"/>
                                </TextBlock>
                            </StackPanel>
                        </StackPanel>

                        <!-- Action Buttons -->
                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <Button Content="🔄 Refresh" Style="{StaticResource GlassButtonStyle}"
                                    Click="RefreshButton_Click" Margin="4,0"/>
                            <Button Content="🛡️ Protect All" Style="{StaticResource GlassButtonStyle}"
                                    Click="ProtectAllButton_Click" Margin="4,0"/>
                            <Button Content="🔓 Unprotect All" Style="{StaticResource GlassButtonStyle}"
                                    Click="UnprotectAllButton_Click" Margin="4,0"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- Roblox Instances List -->
                <Border Grid.Row="2" Style="{StaticResource GlassCardStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Text="Roblox Instances" Style="{StaticResource GlassSubheaderStyle}"/>

                        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto"
                                      HorizontalScrollBarVisibility="Disabled">
                            <StackPanel x:Name="InstancesPanel">
                                <!-- Instances will be added here programmatically -->
                                <Border Style="{StaticResource GlassCardStyle}" Margin="0,4">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <StackPanel Grid.Column="0" VerticalAlignment="Center">
                                            <StackPanel Orientation="Horizontal">
                                                <Ellipse Width="12" Height="12" Fill="{StaticResource AccentGreenBrush}"
                                                         VerticalAlignment="Center" Margin="0,0,8,0"/>
                                                <TextBlock Text="Sample Roblox Instance" Style="{StaticResource GlassSubheaderStyle}" Margin="0"/>
                                            </StackPanel>
                                            <TextBlock Text="PID: 1234 • Started: 12:34:56" Style="{StaticResource GlassTextStyle}"
                                                       Opacity="0.7" FontSize="12" Margin="20,2,0,0"/>
                                            <TextBlock Text="🛡️ Ready for Protection" Style="{StaticResource GlassTextStyle}"
                                                       FontSize="13" FontWeight="Medium" Margin="20,4,0,0"
                                                       Foreground="{StaticResource AccentGreenBrush}"/>
                                        </StackPanel>

                                        <Button Grid.Column="1" Content="🔓 Protect" Style="{StaticResource GlassButtonStyle}"
                                                VerticalAlignment="Center" MinWidth="100" Click="ProtectButton_Click"/>
                                    </Grid>
                                </Border>
                            </StackPanel>
                        </ScrollViewer>

                        <!-- Empty State -->
                        <StackPanel Grid.Row="1" VerticalAlignment="Center" HorizontalAlignment="Center">
                            <TextBlock Text="🎮" FontSize="48" HorizontalAlignment="Center" Opacity="0.3"/>
                            <TextBlock Text="No Roblox instances found" Style="{StaticResource GlassTextStyle}"
                                       FontSize="16" HorizontalAlignment="Center" Opacity="0.7" Margin="0,8,0,4"/>
                            <TextBlock Text="Launch Roblox and click Refresh to get started" Style="{StaticResource GlassTextStyle}"
                                       FontSize="12" HorizontalAlignment="Center" Opacity="0.5"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- Settings Panel -->
                <Border Grid.Row="3" Style="{StaticResource GlassCardStyle}" Margin="0,16,0,0">
                    <Expander Header="⚙️ Settings" Style="{StaticResource GlassExpanderStyle}" IsExpanded="False">
                        <Grid Margin="0,12,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Margin="0,0,16,0">
                                <TextBlock Text="Anti-AFK Settings" Style="{StaticResource GlassTextStyle}"
                                           FontWeight="Medium" Margin="0,0,0,8"/>

                                <Grid Margin="0,0,0,8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="Interval:" Style="{StaticResource GlassTextStyle}"
                                               VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <Slider Grid.Column="1" Value="300" Minimum="60" Maximum="600"
                                            TickFrequency="30" IsSnapToTickEnabled="True" Style="{StaticResource GlassSliderStyle}"/>
                                    <TextBlock Grid.Column="2" Style="{StaticResource GlassTextStyle}"
                                               VerticalAlignment="Center" Margin="8,0,0,0">
                                        <Run Text="300"/>
                                        <Run Text="s"/>
                                    </TextBlock>
                                </Grid>

                                <CheckBox Content="Use random intervals" IsChecked="True"
                                          Style="{StaticResource GlassCheckBoxStyle}" Margin="0,0,0,8"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1">
                                <TextBlock Text="Application Settings" Style="{StaticResource GlassTextStyle}"
                                           FontWeight="Medium" Margin="0,0,0,8"/>

                                <CheckBox Content="Auto-refresh instances" IsChecked="True"
                                          Style="{StaticResource GlassCheckBoxStyle}" Margin="0,0,0,8"/>

                                <Grid Margin="0,0,0,8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="Refresh:" Style="{StaticResource GlassTextStyle}"
                                               VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <Slider Grid.Column="1" Value="10" Minimum="5" Maximum="60"
                                            TickFrequency="5" IsSnapToTickEnabled="True" Style="{StaticResource GlassSliderStyle}"/>
                                    <TextBlock Grid.Column="2" Style="{StaticResource GlassTextStyle}"
                                               VerticalAlignment="Center" Margin="8,0,0,0">
                                        <Run Text="10"/>
                                        <Run Text="s"/>
                                    </TextBlock>
                                </Grid>

                                <CheckBox Content="Minimize to system tray" IsChecked="True"
                                          Style="{StaticResource GlassCheckBoxStyle}"/>
                            </StackPanel>
                        </Grid>
                    </Expander>
                </Border>
            </Grid>
        </Grid>
        </Border>
    </Grid>
</Window>
