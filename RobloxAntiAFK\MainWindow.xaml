<Window x:Class="RobloxAntiAFK.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:RobloxAntiAFK"
        xmlns:controls="clr-namespace:RobloxAntiAFK.Controls"
        xmlns:converters="clr-namespace:RobloxAntiAFK.Converters"
        xmlns:tb="http://www.hardcodet.net/taskbar"
        mc:Ignorable="d"
        Title="Roblox Anti-AFK Manager" Height="700" Width="1000" MinHeight="500" MinWidth="800"
        WindowStyle="None" AllowsTransparency="True" Background="Transparent"
        WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Styles/LiquidGlassStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <converters:BooleanToStringConverter x:Key="BooleanToStringConverter"/>
            <converters:CountToVisibilityConverter x:Key="CountToVisibilityConverter"/>
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        </ResourceDictionary>
    </Window.Resources>

    <Grid>
        <!-- System Tray Icon -->
        <tb:TaskbarIcon x:Name="TrayIcon"
                        ToolTipText="Roblox Anti-AFK Manager"
                        Visibility="Visible">
            <tb:TaskbarIcon.ContextMenu>
                <ContextMenu>
                    <MenuItem Header="Show" Click="ShowWindow_Click"/>
                    <MenuItem Header="Hide" Click="HideWindow_Click"/>
                    <Separator/>
                    <MenuItem Header="Exit" Click="ExitApplication_Click"/>
                </ContextMenu>
            </tb:TaskbarIcon.ContextMenu>
        </tb:TaskbarIcon>

        <!-- Main Window Content -->
        <Border Background="#E6000000" CornerRadius="16">
        <Border.Effect>
            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="8"
                            BlurRadius="24" Opacity="0.6"/>
        </Border.Effect>

        <Grid Margin="2">
            <!-- Background Gradient -->
            <Border CornerRadius="14">
                <Border.Background>
                    <RadialGradientBrush>
                        <GradientStop Color="#FF1A1A1A" Offset="0"/>
                        <GradientStop Color="#FF0D1117" Offset="1"/>
                    </RadialGradientBrush>
                </Border.Background>
            </Border>

            <!-- Content -->
            <Grid Margin="24">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Title Bar -->
                <Grid Grid.Row="0" MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock Text="🎮" FontSize="24" VerticalAlignment="Center" Margin="0,0,8,0"/>
                        <TextBlock Text="Roblox Anti-AFK Manager" Style="{StaticResource GlassHeaderStyle}" Margin="0"/>
                    </StackPanel>

                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <Button Content="−" Style="{StaticResource GlassButtonStyle}"
                                Width="32" Height="32" Padding="0" Margin="4,0"
                                Click="MinimizeButton_Click" FontSize="16"/>
                        <Button Content="✕" Style="{StaticResource GlassButtonStyle}"
                                Width="32" Height="32" Padding="0" Margin="4,0"
                                Click="CloseButton_Click" FontSize="12"/>
                    </StackPanel>
                </Grid>

                <!-- Control Panel -->
                <Border Grid.Row="1" Style="{StaticResource GlassCardStyle}" Margin="0,0,0,16">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- Status Info -->
                        <StackPanel Grid.Column="0" VerticalAlignment="Center">
                            <TextBlock Text="{Binding StatusMessage}" Style="{StaticResource GlassTextStyle}"
                                       FontSize="14" Margin="0,0,0,4"/>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Style="{StaticResource GlassTextStyle}" FontSize="12" Opacity="0.7">
                                    <Run Text="Total: "/>
                                    <Run Text="{Binding TotalCount}" FontWeight="Bold"/>
                                    <Run Text=" • Protected: "/>
                                    <Run Text="{Binding ProtectedCount}" FontWeight="Bold" Foreground="{StaticResource AccentBlueBrush}"/>
                                </TextBlock>
                            </StackPanel>
                        </StackPanel>

                        <!-- Action Buttons -->
                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <Button Content="🔄 Refresh" Style="{StaticResource GlassButtonStyle}"
                                    Command="{Binding RefreshInstancesCommand}" Margin="4,0"/>
                            <Button Content="🛡️ Protect All" Style="{StaticResource GlassButtonStyle}"
                                    Command="{Binding ProtectAllCommand}" Margin="4,0"/>
                            <Button Content="🔓 Unprotect All" Style="{StaticResource GlassButtonStyle}"
                                    Command="{Binding UnprotectAllCommand}" Margin="4,0"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- Roblox Instances List -->
                <Border Grid.Row="2" Style="{StaticResource GlassCardStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Text="Roblox Instances" Style="{StaticResource GlassSubheaderStyle}"/>

                        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto"
                                      HorizontalScrollBarVisibility="Disabled">
                            <ItemsControl ItemsSource="{Binding RobloxInstances}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <controls:RobloxInstanceCard Margin="0,4"/>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                                <ItemsControl.Template>
                                    <ControlTemplate TargetType="ItemsControl">
                                        <Border Background="Transparent">
                                            <ScrollViewer CanContentScroll="False">
                                                <ItemsPresenter/>
                                            </ScrollViewer>
                                        </Border>
                                    </ControlTemplate>
                                </ItemsControl.Template>
                            </ItemsControl>
                        </ScrollViewer>

                        <!-- Empty State -->
                        <StackPanel Grid.Row="1" VerticalAlignment="Center" HorizontalAlignment="Center">
                            <TextBlock Text="🎮" FontSize="48" HorizontalAlignment="Center" Opacity="0.3"/>
                            <TextBlock Text="No Roblox instances found" Style="{StaticResource GlassTextStyle}"
                                       FontSize="16" HorizontalAlignment="Center" Opacity="0.7" Margin="0,8,0,4"/>
                            <TextBlock Text="Launch Roblox and click Refresh to get started" Style="{StaticResource GlassTextStyle}"
                                       FontSize="12" HorizontalAlignment="Center" Opacity="0.5"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- Settings Panel -->
                <Border Grid.Row="3" Style="{StaticResource GlassCardStyle}" Margin="0,16,0,0">
                    <Expander Header="⚙️ Settings" Style="{StaticResource GlassExpanderStyle}" IsExpanded="False">
                        <Grid Margin="0,12,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Margin="0,0,16,0">
                                <TextBlock Text="Anti-AFK Settings" Style="{StaticResource GlassTextStyle}"
                                           FontWeight="Medium" Margin="0,0,0,8"/>

                                <Grid Margin="0,0,0,8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="Interval:" Style="{StaticResource GlassTextStyle}"
                                               VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <Slider Grid.Column="1" Value="{Binding AfkInterval}" Minimum="60" Maximum="600"
                                            TickFrequency="30" IsSnapToTickEnabled="True" Style="{StaticResource GlassSliderStyle}"/>
                                    <TextBlock Grid.Column="2" Style="{StaticResource GlassTextStyle}"
                                               VerticalAlignment="Center" Margin="8,0,0,0">
                                        <Run Text="{Binding AfkInterval}"/>
                                        <Run Text="s"/>
                                    </TextBlock>
                                </Grid>

                                <CheckBox Content="Use random intervals" IsChecked="{Binding UseRandomInterval}"
                                          Style="{StaticResource GlassCheckBoxStyle}" Margin="0,0,0,8"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1">
                                <TextBlock Text="Application Settings" Style="{StaticResource GlassTextStyle}"
                                           FontWeight="Medium" Margin="0,0,0,8"/>

                                <CheckBox Content="Auto-refresh instances" IsChecked="{Binding AutoRefresh}"
                                          Style="{StaticResource GlassCheckBoxStyle}" Margin="0,0,0,8"/>

                                <Grid Margin="0,0,0,8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="Refresh:" Style="{StaticResource GlassTextStyle}"
                                               VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <Slider Grid.Column="1" Value="{Binding RefreshInterval}" Minimum="5" Maximum="60"
                                            TickFrequency="5" IsSnapToTickEnabled="True" Style="{StaticResource GlassSliderStyle}"/>
                                    <TextBlock Grid.Column="2" Style="{StaticResource GlassTextStyle}"
                                               VerticalAlignment="Center" Margin="8,0,0,0">
                                        <Run Text="{Binding RefreshInterval}"/>
                                        <Run Text="s"/>
                                    </TextBlock>
                                </Grid>

                                <CheckBox Content="Minimize to system tray" IsChecked="{Binding MinimizeToTray}"
                                          Style="{StaticResource GlassCheckBoxStyle}"/>
                            </StackPanel>
                        </Grid>
                    </Expander>
                </Border>
            </Grid>
        </Grid>
        </Border>
    </Grid>
</Window>
