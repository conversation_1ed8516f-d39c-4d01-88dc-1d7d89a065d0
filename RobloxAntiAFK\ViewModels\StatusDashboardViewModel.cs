using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows.Threading;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using RobloxAntiAFK.Models;
using RobloxAntiAFK.Services;

namespace RobloxAntiAFK.ViewModels
{
    public partial class StatusDashboardViewModel : ObservableObject
    {
        private readonly LoggingService _loggingService;
        private readonly DispatcherTimer _updateTimer;

        [ObservableProperty]
        private ObservableCollection<LogEntry> _recentLogs;

        [ObservableProperty]
        private ObservableCollection<ProcessStatistics> _processStatistics;

        [ObservableProperty]
        private string _totalActivitiesText;

        [ObservableProperty]
        private string _uptimeText;

        [ObservableProperty]
        private bool _isLoggingEnabled;

        [ObservableProperty]
        private int _maxLogEntries;

        private readonly DateTime _startTime;

        public StatusDashboardViewModel(LoggingService loggingService)
        {
            _loggingService = loggingService;
            _startTime = DateTime.Now;
            
            RecentLogs = new ObservableCollection<LogEntry>();
            ProcessStatistics = new ObservableCollection<ProcessStatistics>();
            
            IsLoggingEnabled = _loggingService.IsLoggingEnabled;
            MaxLogEntries = 100;

            // Subscribe to log events
            _loggingService.LogEntryAdded += OnLogEntryAdded;

            // Setup update timer
            _updateTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            _updateTimer.Tick += UpdateStatistics;
            _updateTimer.Start();

            // Initial load
            LoadRecentLogs();
            UpdateStatistics(null, null);
        }

        private void OnLogEntryAdded(object sender, LogEntry logEntry)
        {
            App.Current?.Dispatcher.Invoke(() =>
            {
                RecentLogs.Insert(0, logEntry);
                
                // Maintain max entries
                while (RecentLogs.Count > MaxLogEntries)
                {
                    RecentLogs.RemoveAt(RecentLogs.Count - 1);
                }
            });
        }

        private void LoadRecentLogs()
        {
            var logs = _loggingService.GetRecentLogs(MaxLogEntries);
            RecentLogs.Clear();
            
            foreach (var log in logs.OrderByDescending(l => l.Timestamp))
            {
                RecentLogs.Add(log);
            }
        }

        private void UpdateStatistics(object sender, EventArgs e)
        {
            // Update uptime
            var uptime = DateTime.Now - _startTime;
            UptimeText = $"{uptime.Days}d {uptime.Hours:D2}h {uptime.Minutes:D2}m {uptime.Seconds:D2}s";

            // Update total activities
            var totalActivities = RecentLogs.Count(log => log.Level == LogLevel.Activity);
            TotalActivitiesText = $"{totalActivities} activities performed";
        }

        [RelayCommand]
        private void ClearLogs()
        {
            _loggingService.ClearLogs();
            RecentLogs.Clear();
        }

        [RelayCommand]
        private async void ExportLogs()
        {
            try
            {
                var exportPath = await _loggingService.ExportLogsAsync();
                _loggingService.LogInfo($"Logs exported to: {exportPath}");
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Failed to export logs", ex);
            }
        }

        [RelayCommand]
        private void ToggleLogging()
        {
            _loggingService.IsLoggingEnabled = IsLoggingEnabled;
            var status = IsLoggingEnabled ? "enabled" : "disabled";
            _loggingService.LogInfo($"Logging {status}");
        }

        partial void OnMaxLogEntriesChanged(int value)
        {
            _loggingService.MaxRecentLogs = Math.Max(50, value);
            LoadRecentLogs();
        }

        public void UpdateProcessStatistics(ObservableCollection<RobloxInstance> instances)
        {
            ProcessStatistics.Clear();
            
            foreach (var instance in instances)
            {
                var stats = new ProcessStatistics
                {
                    ProcessId = instance.ProcessId,
                    DisplayName = instance.DisplayName,
                    IsProtected = instance.IsProtected,
                    IsRunning = instance.IsRunning,
                    StartTime = instance.StartTime,
                    LastActivity = instance.LastActivity,
                    ActivityCount = RecentLogs.Count(log => log.ProcessId == instance.ProcessId && log.Level == LogLevel.Activity)
                };
                
                ProcessStatistics.Add(stats);
            }
        }

        public void Cleanup()
        {
            _updateTimer?.Stop();
            if (_loggingService != null)
            {
                _loggingService.LogEntryAdded -= OnLogEntryAdded;
            }
        }
    }

    public class ProcessStatistics
    {
        public int ProcessId { get; set; }
        public string DisplayName { get; set; }
        public bool IsProtected { get; set; }
        public bool IsRunning { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime LastActivity { get; set; }
        public int ActivityCount { get; set; }

        public string StatusText => IsRunning ? (IsProtected ? "🛡️ Protected" : "⚠️ Unprotected") : "❌ Not Running";
        public string UptimeText
        {
            get
            {
                var uptime = DateTime.Now - StartTime;
                return $"{uptime.Hours:D2}h {uptime.Minutes:D2}m";
            }
        }
        public string LastActivityText => LastActivity == default ? "Never" : LastActivity.ToString("HH:mm:ss");
    }
}
