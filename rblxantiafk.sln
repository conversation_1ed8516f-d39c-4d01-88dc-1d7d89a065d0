Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.2.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RobloxAntiAFK", "RobloxAntiAFK\RobloxAntiAFK.csproj", "{83499F6D-A308-94F4-065A-2BF840A81762}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{83499F6D-A308-94F4-065A-2BF840A81762}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{83499F6D-A308-94F4-065A-2BF840A81762}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{83499F6D-A308-94F4-065A-2BF840A81762}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{83499F6D-A308-94F4-065A-2BF840A81762}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {FCD5A109-1BB0-4231-B9DB-2DD3A67A8246}
	EndGlobalSection
EndGlobal
